<?php
/**
 * TX PROPERTIES - Functions for property operations
 */

/**
 * Get latest properties from database
 * 
 * @param int $limit Number of properties to retrieve
 * @param bool $featured Whether to get only featured properties
 * @return array Array of property data
 */
function getLatestProperties($limit = 6, $featured = false) {
    global $conn;

    $featuredCondition = $featured ? " AND featured = 1" : "";
    // Check if property_rentals table exists
    $tableExists = $conn->query("SHOW TABLES LIKE 'property_rentals'")->num_rows > 0;

    if ($tableExists) {
        $sql = "SELECT p.*, u.username as seller_name,
                      (SELECT image_path FROM property_images WHERE property_id = p.id AND is_primary = 1 LIMIT 1) as primary_image,
                      (SELECT COUNT(*) FROM property_rentals pr WHERE pr.property_id = p.id AND pr.status = 'active' AND pr.rental_end_date >= CURDATE()) as is_rented
               FROM properties p
               JOIN users u ON p.seller_id = u.id
               WHERE p.status = 'Available'{$featuredCondition}
               ORDER BY p.created_at DESC
               LIMIT ?";
    } else {
        $sql = "SELECT p.*, u.username as seller_name,
                      (SELECT image_path FROM property_images WHERE property_id = p.id AND is_primary = 1 LIMIT 1) as primary_image,
                      0 as is_rented
               FROM properties p
               JOIN users u ON p.seller_id = u.id
               WHERE p.status = 'Available'{$featuredCondition}
               ORDER BY p.created_at DESC
               LIMIT ?";
    }

    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $limit);
    $stmt->execute();
    $result = $stmt->get_result();

    $properties = [];
    while ($row = $result->fetch_assoc()) {
        // If no primary image is set, use a default image
        if (empty($row['primary_image'])) {
            $row['primary_image'] = 'assets/images/property-default.jpg';
        }

        // Add rental status
        $row['rental_status'] = ($row['listing_type'] === 'Rent' && $row['is_rented'] > 0) ? 'Rented' : 'Available';

        // Format the location display (local_area, location)
        if (!empty($row['local_area'])) {
            $row['location_display'] = $row['local_area'] . ', ' . $row['location'];
        } else {
            $row['location_display'] = $row['location'];
        }
        
        $properties[] = $row;
    }
    
    return $properties;
}

/**
 * Get all property locations for dropdowns
 */
function getPropertyLocations() {
    // Return fixed list of Tanzania regions
    return [
        'Arusha',
        'Dar es Salaam',
        'Dodoma',
        'Mbeya',
        'Tanga',
        'Zanzibar'
    ];
}

/**
 * Get all property types for dropdowns
 */
function getPropertyTypes() {
    // Return all available property types
    return [
        'Land',
        'House',
        'Apartment',
        'Commercial',
        'Office',
        'Office Space',
        'Store/Shop'
    ];
}

/**
 * Search properties by criteria
 * 
 * @param array $criteria Search criteria
 * @return array Array of matching properties
 */
function searchProperties($criteria) {
    global $conn;
    
    $conditions = [];
    $params = [];
    $types = "";
    
    // Build search conditions
    if (!empty($criteria['location'])) {
        $conditions[] = "p.location = ?";
        $params[] = $criteria['location'];
        $types .= "s";
    }
    
    if (!empty($criteria['type'])) {
        $conditions[] = "p.property_type = ?";
        $params[] = $criteria['type'];
        $types .= "s";
    }
    
    if (!empty($criteria['listing'])) {
        $conditions[] = "p.listing_type = ?";
        $params[] = $criteria['listing'];
        $types .= "s";
    }
    
    // Always get available properties
    $conditions[] = "p.status = 'Available'";
    
    // Combine all conditions
    $whereClause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";
    
    // Check if property_rentals table exists
    $tableExists = $conn->query("SHOW TABLES LIKE 'property_rentals'")->num_rows > 0;

    if ($tableExists) {
        $sql = "SELECT p.*, u.username as seller_name,
                      (SELECT image_path FROM property_images WHERE property_id = p.id AND is_primary = 1 LIMIT 1) as primary_image,
                      (SELECT COUNT(*) FROM property_rentals pr WHERE pr.property_id = p.id AND pr.status = 'active' AND pr.rental_end_date >= CURDATE()) as is_rented
               FROM properties p
               JOIN users u ON p.seller_id = u.id
               {$whereClause}
               ORDER BY p.created_at DESC";
    } else {
        $sql = "SELECT p.*, u.username as seller_name,
                      (SELECT image_path FROM property_images WHERE property_id = p.id AND is_primary = 1 LIMIT 1) as primary_image,
                      0 as is_rented
               FROM properties p
               JOIN users u ON p.seller_id = u.id
               {$whereClause}
               ORDER BY p.created_at DESC";
    }
    
    $stmt = $conn->prepare($sql);
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $properties = [];
    while ($row = $result->fetch_assoc()) {
        // If no primary image is set, use a default image
        if (empty($row['primary_image'])) {
            $row['primary_image'] = 'assets/images/property-default.jpg';
        }

        // Add rental status
        $row['rental_status'] = ($row['listing_type'] === 'Rent' && $row['is_rented'] > 0) ? 'Rented' : 'Available';

        // Format the location display (local_area, location)
        if (!empty($row['local_area'] ?? '')) {
            $row['location_display'] = $row['local_area'] . ', ' . $row['location'];
        } else {
            $row['location_display'] = $row['location'];
        }
        
        $properties[] = $row;
    }
    
    return $properties;
}

/**
 * Get property details by ID
 * 
 * @param int $id Property ID
 * @return array|null Property details or null if not found
 */
function getPropertyById($id) {
    global $conn;
    // Check if additional feature columns exist
    $columnsResult = $conn->query("SHOW COLUMNS FROM properties LIKE 'has_electricity'");
    $hasFeatures = $columnsResult && $columnsResult->num_rows > 0;
    if ($hasFeatures) {
        $sql = "SELECT p.*, u.username as seller_name, u.email as seller_email, u.phone as seller_phone,
            p.has_electricity, p.has_water, p.has_fence, p.has_gym, p.has_air_condition, p.has_security, p.has_pool
            FROM properties p
            JOIN users u ON p.seller_id = u.id
            WHERE p.id = ? AND p.status != 'Sold'";
    } else {
        $sql = "SELECT p.*, u.username as seller_name, u.email as seller_email, u.phone as seller_phone
            FROM properties p
            JOIN users u ON p.seller_id = u.id
            WHERE p.id = ? AND p.status != 'Sold'";
    }
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows === 0) {
        return null;
    }
    $property = $result->fetch_assoc();
    // Track unique view
    trackPropertyView($id);
    return $property;
}

/**
 * Track unique property view
 * 
 * @param int $propertyId Property ID
 * @return void
 */
function trackPropertyView($propertyId) {
    global $conn;
    
    $userId = isLoggedIn() ? $_SESSION['user_id'] : null;
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    try {
        // Try to insert view record (will fail if duplicate due to UNIQUE constraint)
        $sql = "INSERT INTO property_views (property_id, user_id, ip_address, user_agent) 
                VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("iiss", $propertyId, $userId, $ipAddress, $userAgent);
        $stmt->execute();
        
        // If successful, update the views count in properties table
        $updateSql = "UPDATE properties SET views = (
            SELECT COUNT(DISTINCT CASE 
                WHEN user_id IS NOT NULL THEN user_id 
                ELSE ip_address 
            END) 
            FROM property_views 
            WHERE property_id = ?
        ) WHERE id = ?";
        $updateStmt = $conn->prepare($updateSql);
        $updateStmt->bind_param("ii", $propertyId, $propertyId);
        $updateStmt->execute();
        
    } catch (Exception $e) {
        // View already tracked for this user/IP or table doesn't exist, ignore
        error_log("View tracking error: " . $e->getMessage());
    }
}

/**
 * Get property images by property ID
 * 
 * @param int $propertyId Property ID
 * @return array Array of image paths
 */
function getPropertyImages($propertyId) {
    global $conn;
    
    $sql = "SELECT * FROM property_images WHERE property_id = ? ORDER BY is_primary DESC";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $propertyId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $images = [];
    while ($row = $result->fetch_assoc()) {
        $images[] = $row;
    }
    
    // If no images found, return a default image
    if (empty($images)) {
        $images[] = [
            'id' => 0,
            'property_id' => $propertyId,
            'image_path' => 'assets/images/property-default.jpg',
            'is_primary' => 1
        ];
    }
    
    return $images;
}

/**
 * Format price based on listing type
 * 
 * @param float $price The price amount
 * @param string $listingType 'Sale' or 'Rent'
 * @return string Formatted price with appropriate suffix
 */
function formatPrice($price, $listingType) {
    $formattedPrice = number_format($price, 0, '.', ',');
    
    if ($listingType === 'Rent') {
        return "TZS " . $formattedPrice . "/month";
    } else {
        return "TZS " . $formattedPrice;
    }
}

/**
 * Function to protect seller-only pages
 * 
 * @param string $redirect_to The page to redirect to after login
 * @return void
 */
function require_seller_login($redirect_to = '') {
    // Check if user is logged in
    if (!isLoggedIn()) {
        $redirectUrl = 'login.php';
        if (!empty($redirect_to)) {
            $redirectUrl .= '?redirect=' . urlencode($redirect_to);
        }
        redirect($redirectUrl);
    }
    
    // Check if user is a seller
    if (!isSeller()) {
        // If logged in but not a seller, redirect to home with message
        $_SESSION['error_message'] = "You must be a seller to access this page.";
        redirect('index.php');
    }
}

/**
 * Function to process payment
 *
 * @param int $propertyId The property ID
 * @param int $buyerId The buyer ID
 * @param float $amount The payment amount
 * @param string $paymentMethod The payment method
 * @return bool|string True if successful, error message if failed
 */
function processPayment($propertyId, $buyerId, $amount, $paymentMethod = 'Flutterwave') {
    global $conn;
    
    // Get property details
    $sql = "SELECT seller_id, listing_type FROM properties WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $propertyId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        return "Property not found.";
    }
    
    $property = $result->fetch_assoc();
    $sellerId = $property['seller_id'];
    
    // Start transaction
    $conn->begin_transaction();
    
    try {
        // Generate transaction ID
        $transactionId = 'TXN' . time() . rand(1000, 9999);
        
        // Insert payment record
        $sql = "INSERT INTO payments (property_id, buyer_id, seller_id, amount, status, transaction_id, payment_method) 
                VALUES (?, ?, ?, ?, 'completed', ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("iiidss", $propertyId, $buyerId, $sellerId, $amount, $transactionId, $paymentMethod);
        $stmt->execute();
        
        // Update seller's balance
        $sql = "UPDATE users SET balance = balance + ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("di", $amount, $sellerId);
        $stmt->execute();
        
        // Update property status if it's a sale (not for rent)
        if ($property['listing_type'] === 'Sale') {
            $sql = "UPDATE properties SET status = 'Sold' WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $propertyId);
            $stmt->execute();
        } else {
            // For rentals, mark as reserved
            $sql = "UPDATE properties SET status = 'Reserved' WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $propertyId);
            $stmt->execute();
        }
        
        // Commit transaction
        $conn->commit();
        return true;
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        return "Payment failed: " . $e->getMessage();
    }
}

/**
 * Count unread messages for a user
 * 
 * @param int $userId The user ID
 * @return int Number of unread messages
 */
function countUnreadMessages($userId) {
    global $conn;
    
    $sql = "SELECT COUNT(*) as unread_count FROM messages WHERE receiver_id = ? AND read_status = 0";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $data = $result->fetch_assoc();
    
    return (int) $data['unread_count'];
}

/**
 * Get total unique views for a seller's properties
 * 
 * @param int $sellerId The seller ID
 * @return int Total unique views
 */
function getSellerTotalViews($sellerId) {
    global $conn;
    
    try {
        $sql = "SELECT COUNT(DISTINCT CASE 
                    WHEN pv.user_id IS NOT NULL THEN pv.user_id 
                    ELSE pv.ip_address 
                END) as total_views
                FROM property_views pv
                JOIN properties p ON pv.property_id = p.id
                WHERE p.seller_id = ? AND p.status != 'Sold'";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $sellerId);
        $stmt->execute();
        $result = $stmt->get_result();
        $data = $result->fetch_assoc();
        
        return (int) $data['total_views'];
    } catch (Exception $e) {
        // If property_views table doesn't exist yet, return 0
        error_log("getSellerTotalViews error: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get total earnings for a seller from completed sales
 * 
 * @param int $sellerId The seller ID
 * @return float Total earnings
 */
function getSellerTotalEarnings($sellerId) {
    global $conn;
    
    try {
        $sql = "SELECT COALESCE(SUM(p.amount), 0) as total_earnings
                FROM payments p
                JOIN properties prop ON p.property_id = prop.id
                WHERE p.seller_id = ? 
                AND p.status = 'completed' 
                AND prop.listing_type = 'Sale'";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $sellerId);
        $stmt->execute();
        $result = $stmt->get_result();
        $data = $result->fetch_assoc();
        
        return (float) $data['total_earnings'];
    } catch (Exception $e) {
        // If payments table doesn't exist or other error, return 0
        error_log("getSellerTotalEarnings error: " . $e->getMessage());
        return 0.0;
    }
}

/**
 * Update property status to Sold when payment is completed
 *
 * @param int $propertyId Property ID
 * @return bool Success status
 */
function markPropertyAsSold($propertyId) {
    global $conn;

    $sql = "UPDATE properties SET status = 'Sold' WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $propertyId);

    return $stmt->execute();
}

/**
 * Check if a property has been paid for by a specific buyer
 *
 * @param int $propertyId Property ID
 * @param int $buyerId Buyer ID (optional, if not provided checks any buyer)
 * @return array|false Payment details if paid, false if not paid
 */
function getPropertyPaymentStatus($propertyId, $buyerId = null) {
    global $conn;

    $sql = "SELECT p.*, u.username as buyer_name
            FROM payments p
            JOIN users u ON p.buyer_id = u.id
            WHERE p.property_id = ? AND p.status = 'completed'";
    $params = [$propertyId];
    $types = "i";

    if ($buyerId !== null) {
        $sql .= " AND p.buyer_id = ?";
        $params[] = $buyerId;
        $types .= "i";
    }

    $sql .= " ORDER BY p.date DESC LIMIT 1";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return false;
}

/**
 * Check if current user can pay for a property
 *
 * @param array $property Property details
 * @return array Status array with 'can_pay' boolean and 'message' string
 */
function canUserPayForProperty($property) {
    // Check if user is logged in
    if (!isLoggedIn()) {
        return [
            'can_pay' => false,
            'message' => 'Please login to make a payment'
        ];
    }

    // Check if user is a buyer
    if ($_SESSION['user_role'] !== 'buyer') {
        return [
            'can_pay' => false,
            'message' => 'Only buyers can make payments'
        ];
    }

    // Check if property is available
    if ($property['status'] !== 'Available') {
        return [
            'can_pay' => false,
            'message' => 'Property is no longer available'
        ];
    }

    // Check if property is for sale (not rent)
    if ($property['listing_type'] !== 'Sale') {
        return [
            'can_pay' => false,
            'message' => 'Payment is only available for properties for sale'
        ];
    }

    // Check if user is not the seller
    if ($property['seller_id'] == $_SESSION['user_id']) {
        return [
            'can_pay' => false,
            'message' => 'You cannot buy your own property'
        ];
    }

    // Check if property has already been paid for
    $paymentStatus = getPropertyPaymentStatus($property['id']);
    if ($paymentStatus) {
        return [
            'can_pay' => false,
            'message' => 'Property has already been purchased by ' . htmlspecialchars($paymentStatus['buyer_name'])
        ];
    }

    return [
        'can_pay' => true,
        'message' => 'Ready to purchase'
    ];
}
?>