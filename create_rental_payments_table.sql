-- Create rental_payments table for rental property transactions
CREATE TABLE IF NOT EXISTS rental_payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    property_id INT NOT NULL,
    tenant_id INT NOT NULL,
    landlord_id INT NOT NULL,
    monthly_rate DECIMAL(10,2) NOT NULL,
    rent_duration INT NOT NULL COMMENT 'Duration in months',
    total_amount DECIMAL(10,2) NOT NULL,
    payment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    rental_start_date DATE NOT NULL,
    rental_end_date DATE NOT NULL,
    transaction_id VARCHAR(255) NOT NULL,
    payment_method VARCHAR(50) DEFAULT 'Flutterwave',
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'completed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
    FOREIGN KEY (tenant_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (landlord_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes for better performance
    INDEX idx_property_id (property_id),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_landlord_id (landlord_id),
    INDEX idx_rental_dates (rental_start_date, rental_end_date),
    INDEX idx_transaction_id (transaction_id)
);

-- Create property_rentals table to track current rental status
CREATE TABLE IF NOT EXISTS property_rentals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    property_id INT NOT NULL UNIQUE,
    tenant_id INT NOT NULL,
    landlord_id INT NOT NULL,
    rental_payment_id INT NOT NULL,
    rental_start_date DATE NOT NULL,
    rental_end_date DATE NOT NULL,
    monthly_rate DECIMAL(10,2) NOT NULL,
    status ENUM('active', 'expired', 'terminated') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
    FOREIGN KEY (tenant_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (landlord_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (rental_payment_id) REFERENCES rental_payments(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_property_rental (property_id),
    INDEX idx_tenant_rental (tenant_id),
    INDEX idx_rental_dates (rental_start_date, rental_end_date),
    INDEX idx_status (status)
);

-- Insert sample data for testing (optional)
-- You can run this after creating the tables if you want test data

-- Sample rental payment
-- INSERT INTO rental_payments (property_id, tenant_id, landlord_id, monthly_rate, rent_duration, total_amount, rental_start_date, rental_end_date, transaction_id) 
-- VALUES (1, 2, 1, 50000.00, 6, 300000.00, '2024-01-01', '2024-06-30', 'RENT_TEST_001');

-- Sample active rental
-- INSERT INTO property_rentals (property_id, tenant_id, landlord_id, rental_payment_id, rental_start_date, rental_end_date, monthly_rate) 
-- VALUES (1, 2, 1, 1, '2024-01-01', '2024-06-30', 50000.00);
