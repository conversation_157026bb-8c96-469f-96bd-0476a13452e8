<?php
require_once 'includes/config.php';
header('Content-Type: application/json');

if (!isLoggedIn() || $_SESSION['user_role'] !== 'buyer') {
    echo json_encode(['success' => false, 'message' => 'Not authorized.']);
    exit;
}

$userId = $_SESSION['user_id'];
$propertyId = isset($_POST['property_id']) ? (int)$_POST['property_id'] : 0;

if (!$propertyId) {
    echo json_encode(['success' => false, 'message' => 'Invalid property.']);
    exit;
}

// Check if already favorited
$sql = "SELECT id FROM favorites WHERE buyer_id = ? AND property_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('ii', $userId, $propertyId);
$stmt->execute();
$stmt->store_result();

if ($stmt->num_rows > 0) {
    // Remove from favorites
    $sql = "DELETE FROM favorites WHERE buyer_id = ? AND property_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('ii', $userId, $propertyId);
    $stmt->execute();
    echo json_encode(['success' => true, 'favorited' => false, 'message' => 'Removed from favorites.']);
    exit;
} else {
    // Add to favorites
    $sql = "INSERT INTO favorites (buyer_id, property_id, created_at) VALUES (?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('ii', $userId, $propertyId);
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'favorited' => true, 'message' => 'Added to favorites.']);
        exit;
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to add to favorites.']);
        exit;
    }
} 