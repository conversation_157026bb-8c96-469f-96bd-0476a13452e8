<?php
session_start();
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}
require_once '../includes/config.php';
include 'admin_header.php';

// Handle delete
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $id = (int)$_GET['delete'];
    $conn->query("DELETE FROM messages WHERE sender_id = $id OR receiver_id = $id");
    $conn->query("DELETE FROM payments WHERE seller_id = $id OR buyer_id = $id");
    $conn->query("DELETE FROM users WHERE id = $id");
    echo '<div class="alert alert-success">User deleted.</div>';
}

// Handle role change
if (isset($_GET['role']) && isset($_GET['id']) && is_numeric($_GET['id'])) {
    $id = (int)$_GET['id'];
    $role = $_GET['role'];
    if (in_array($role, ['admin','seller','buyer'])) {
        $conn->query("UPDATE users SET role = '$role' WHERE id = $id");
        echo '<div class="alert alert-success">User role updated.</div>';
    }
}

// Fetch all users
$sql = "SELECT * FROM users ORDER BY created_at DESC";
$result = $conn->query($sql);
?>
<div class="container-fluid">
    <h1 class="mb-4">Manage Users</h1>
    <div class="table-responsive">
        <table class="table table-bordered table-hover">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Username</th>
                    <th>Email</th>
                    <th>Role</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($row = $result->fetch_assoc()): ?>
                <tr>
                    <td><?php echo $row['id']; ?></td>
                    <td><?php echo htmlspecialchars($row['username']); ?></td>
                    <td><?php echo htmlspecialchars($row['email']); ?></td>
                    <td><?php echo htmlspecialchars($row['role']); ?></td>
                    <td><?php echo isset($row['status']) ? htmlspecialchars($row['status']) : 'Active'; ?></td>
                    <td>
                        <a href="edit_user.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-primary">Edit</a>
                        <a href="users.php?delete=<?php echo $row['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Delete this user?');">Delete</a>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">Change Role</button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="users.php?id=<?php echo $row['id']; ?>&role=admin">Admin</a></li>
                                <li><a class="dropdown-item" href="users.php?id=<?php echo $row['id']; ?>&role=seller">Seller</a></li>
                                <li><a class="dropdown-item" href="users.php?id=<?php echo $row['id']; ?>&role=buyer">Buyer</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
    </div>
</div>
<?php include 'admin_footer.php'; ?> 