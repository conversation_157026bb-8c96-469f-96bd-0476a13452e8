<?php
session_start();
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}
require_once '../includes/config.php';
include 'admin_header.php';

// Handle delete
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $id = (int)$_GET['delete'];
    $conn->query("DELETE FROM payments WHERE property_id = $id");
    $conn->query("DELETE FROM messages WHERE property_id = $id");
    $conn->query("DELETE FROM properties WHERE id = $id");
    echo '<div class="alert alert-success">Property deleted.</div>';
}

// Fetch all properties
$sql = "SELECT p.*, u.username as seller_name FROM properties p LEFT JOIN users u ON p.seller_id = u.id ORDER BY p.created_at DESC";
$result = $conn->query($sql);
?>
<div class="container-fluid">
    <h1 class="mb-4">Manage Properties</h1>
    <div class="table-responsive">
        <table class="table table-bordered table-hover">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Title</th>
                    <th>Type</th>
                    <th>Price</th>
                    <th>Status</th>
                    <th>Seller</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($row = $result->fetch_assoc()): ?>
                <tr>
                    <td><?php echo $row['id']; ?></td>
                    <td><?php echo htmlspecialchars($row['title']); ?></td>
                    <td><?php echo htmlspecialchars($row['property_type']); ?></td>
                    <td><?php echo number_format($row['price']); ?></td>
                    <td><?php echo htmlspecialchars($row['status']); ?></td>
                    <td><?php echo htmlspecialchars($row['seller_name']); ?></td>
                    <td>
                        <a href="edit_property.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-primary">Edit</a>
                        <a href="properties.php?delete=<?php echo $row['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Delete this property?');">Delete</a>
                    </td>
                </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
    </div>
</div>
<?php include 'admin_footer.php'; ?> 