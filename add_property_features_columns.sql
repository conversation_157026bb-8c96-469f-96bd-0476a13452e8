-- Add Property Features Columns to TX Properties Database
-- Run this SQL script in phpMyAdmin or MySQL command line

USE txproperties;

-- Check if columns exist and add them if they don't
-- Note: MySQL will give an error if column already exists, but that's okay

-- Add has_electricity column
ALTER TABLE properties 
ADD COLUMN has_electricity TINYINT(1) DEFAULT 0 
COMMENT 'Property has electricity (1=Yes, 0=No)';

-- Add has_water column  
ALTER TABLE properties 
ADD COLUMN has_water TINYINT(1) DEFAULT 0 
COMMENT 'Property has water supply (1=Yes, 0=No)';

-- Add has_fence column
ALTER TABLE properties 
ADD COLUMN has_fence TINYINT(1) DEFAULT 0 
COMMENT 'Property has fence (1=Yes, 0=No)';

-- Add has_gym column (if not already exists)
ALTER TABLE properties 
ADD COLUMN has_gym TINYINT(1) DEFAULT 0 
COMMENT 'Property has gym facilities (1=Yes, 0=No)';

-- Add has_air_condition column (if not already exists)
ALTER TABLE properties 
ADD COLUMN has_air_condition TINYINT(1) DEFAULT 0 
COMMENT 'Property has air conditioning (1=Yes, 0=No)';

-- Add has_security column (if not already exists)
ALTER TABLE properties 
ADD COLUMN has_security TINYINT(1) DEFAULT 0 
COMMENT 'Property has security features (1=Yes, 0=No)';

-- Add has_pool column (if not already exists)
ALTER TABLE properties 
ADD COLUMN has_pool TINYINT(1) DEFAULT 0 
COMMENT 'Property has swimming pool (1=Yes, 0=No)';

-- Add some test data to the first property
UPDATE properties 
SET has_electricity = 1, 
    has_water = 1, 
    has_fence = 0, 
    has_gym = 1, 
    has_air_condition = 0, 
    has_security = 1, 
    has_pool = 0 
WHERE id = 1;

-- Add test data to second property (different features)
UPDATE properties 
SET has_electricity = 1, 
    has_water = 1, 
    has_fence = 1, 
    has_gym = 0, 
    has_air_condition = 1, 
    has_security = 1, 
    has_pool = 1 
WHERE id = 2;

-- Verify the changes
SELECT id, title, has_electricity, has_water, has_fence, has_gym, has_air_condition, has_security, has_pool 
FROM properties 
LIMIT 5;

-- Show table structure to confirm columns were added
DESCRIBE properties;
