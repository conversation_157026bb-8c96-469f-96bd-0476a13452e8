<?php
// Database connection configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', 'password');
define('DB_NAME', 'txproperties');

// Establish database connection
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Set character set
$conn->set_charset("utf8");

// Session start
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Base URL of the website
define('BASE_URL', 'http://localhost/txonline/');

// Function to sanitize input data
function sanitize($data) {
    global $conn;
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $conn->real_escape_string($data);
}

// Function to check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Function to check if user has seller role
function isSeller() {
    return isLoggedIn() && $_SESSION['user_role'] == 'seller';
}

// Function to check if user has admin role
function isAdmin() {
    return isLoggedIn() && $_SESSION['user_role'] == 'admin';
}

// Function to redirect to a specific page
function redirect($location) {
    header("Location: " . BASE_URL . $location);
    exit;
}

// Function to display error messages
function displayError($message) {
    return '<div class="alert alert-danger">' . $message . '</div>';
}

// Function to display success messages
function displaySuccess($message) {
    return '<div class="alert alert-success">' . $message . '</div>';
}
?> 