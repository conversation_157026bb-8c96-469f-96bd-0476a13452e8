<?php
/**
 * Email Helper for TX PROPERTIES
 * Handles payment confirmation emails
 */

/**
 * Send payment confirmation email using PHP mail function
 */
function sendPaymentConfirmationEmail($paymentData) {
    $to = $paymentData['email'];
    $subject = 'Payment Confirmation - TX Properties';
    
    // Generate email body
    $emailBody = generatePaymentEmailBody($paymentData);
    
    // Email headers
    $headers = array();
    $headers[] = 'MIME-Version: 1.0';
    $headers[] = 'Content-type: text/html; charset=UTF-8';
    $headers[] = 'From: TX Properties <<EMAIL>>';
    $headers[] = 'Reply-To: <EMAIL>';
    $headers[] = 'X-Mailer: PHP/' . phpversion();
    
    // For debugging, log the email attempt
    error_log("Attempting to send email to: " . $to);
    error_log("Email subject: " . $subject);
    
    // Send email
    $sent = mail($to, $subject, $emailBody, implode("\r\n", $headers));
    
    if ($sent) {
        error_log("Email sent successfully to: " . $to);
    } else {
        error_log("Failed to send email to: " . $to);
        // Try alternative method for local development
        if (isLocalEnvironment()) {
            $sent = sendLocalEmail($to, $subject, $emailBody);
        }
    }
    
    return $sent;
}

/**
 * Check if running in local environment
 */
function isLocalEnvironment() {
    return in_array($_SERVER['HTTP_HOST'] ?? '', ['localhost', '127.0.0.1', '::1']) || 
           strpos($_SERVER['HTTP_HOST'] ?? '', 'localhost') !== false;
}

/**
 * Alternative email method for local development
 */
function sendLocalEmail($to, $subject, $body) {
    // For local development, just log the email content
    error_log("=== LOCAL EMAIL SIMULATION ===");
    error_log("To: " . $to);
    error_log("Subject: " . $subject);
    error_log("Body: " . substr($body, 0, 500) . "...");
    error_log("=== END EMAIL SIMULATION ===");
    
    // Create a simple email file for testing
    $emailFile = 'email_log_' . date('Y-m-d_H-i-s') . '.html';
    $emailContent = "
    <html>
    <head><title>Email Log</title></head>
    <body>
        <h2>Email Sent at: " . date('Y-m-d H:i:s') . "</h2>
        <p><strong>To:</strong> {$to}</p>
        <p><strong>Subject:</strong> {$subject}</p>
        <hr>
        {$body}
    </body>
    </html>";
    
    file_put_contents($emailFile, $emailContent);
    error_log("Email content saved to: " . $emailFile);
    
    return true; // Simulate success
}

/**
 * Generate payment confirmation email body
 */
function generatePaymentEmailBody($paymentData) {
    $amount = number_format($paymentData['amount'], 2);
    $date = date('F j, Y \a\t g:i A');
    
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='utf-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>Payment Confirmation</title>
        <style>
            body { 
                font-family: Arial, sans-serif; 
                line-height: 1.6; 
                color: #333; 
                margin: 0; 
                padding: 0; 
                background-color: #f4f4f4;
            }
            .container { 
                max-width: 600px; 
                margin: 0 auto; 
                background-color: #ffffff;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }
            .header { 
                background: #2c5530; 
                color: white; 
                padding: 30px 20px; 
                text-align: center; 
            }
            .header h1 { 
                margin: 0; 
                font-size: 28px; 
            }
            .header p { 
                margin: 10px 0 0 0; 
                opacity: 0.9; 
            }
            .content { 
                padding: 40px 20px; 
            }
            .success-message { 
                text-align: center; 
                margin-bottom: 30px; 
            }
            .success-message h2 { 
                color: #28a745; 
                margin-bottom: 10px; 
            }
            .details { 
                background: #f8f9fa; 
                padding: 25px; 
                margin: 25px 0; 
                border-radius: 8px; 
                border-left: 4px solid #2c5530;
            }
            .detail-row { 
                display: flex; 
                justify-content: space-between; 
                margin-bottom: 15px; 
                padding-bottom: 10px; 
                border-bottom: 1px solid #e9ecef;
            }
            .detail-row:last-child { 
                border-bottom: none; 
                margin-bottom: 0; 
            }
            .detail-label { 
                font-weight: bold; 
                color: #495057; 
            }
            .detail-value { 
                color: #2c5530; 
            }
            .amount { 
                font-size: 24px; 
                color: #28a745; 
                font-weight: bold; 
            }
            .next-steps { 
                background: #e3f2fd; 
                padding: 25px; 
                margin: 25px 0; 
                border-radius: 8px; 
            }
            .next-steps h3 { 
                color: #1976d2; 
                margin-top: 0; 
            }
            .step { 
                margin-bottom: 15px; 
                padding-left: 20px; 
            }
            .step:last-child { 
                margin-bottom: 0; 
            }
            .footer { 
                background: #333; 
                color: white; 
                padding: 20px; 
                text-align: center; 
                font-size: 12px; 
            }
            .footer p { 
                margin: 5px 0; 
            }
            .security-notice { 
                background: #fff3cd; 
                border: 1px solid #ffeaa7; 
                padding: 15px; 
                margin: 20px 0; 
                border-radius: 5px; 
                color: #856404; 
            }
            @media (max-width: 600px) {
                .detail-row { 
                    flex-direction: column; 
                }
                .detail-value { 
                    margin-top: 5px; 
                }
            }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>TX Properties</h1>
                <p>Payment Confirmation</p>
            </div>
            
            <div class='content'>
                <div class='success-message'>
                    <h2>✓ Payment Successful!</h2>
                    <p>Dear {$paymentData['name']},</p>
                    <p>We have successfully received your payment. Thank you for choosing TX Properties!</p>
                </div>
                
                <div class='details'>
                    <h3 style='margin-top: 0; color: #2c5530;'>Transaction Details</h3>
                    
                    <div class='detail-row'>
                        <span class='detail-label'>Payment Status:</span>
                        <span class='detail-value' style='color: #28a745; font-weight: bold;'>SUCCESS</span>
                    </div>
                    
                    <div class='detail-row'>
                        <span class='detail-label'>Transaction ID:</span>
                        <span class='detail-value'>{$paymentData['transaction_id']}</span>
                    </div>
                    
                    <div class='detail-row'>
                        <span class='detail-label'>Amount Paid:</span>
                        <span class='detail-value amount'>TZS {$amount}</span>
                    </div>
                    
                    <div class='detail-row'>
                        <span class='detail-label'>Payment Date:</span>
                        <span class='detail-value'>{$date}</span>
                    </div>
                    
                    <div class='detail-row'>
                        <span class='detail-label'>Property:</span>
                        <span class='detail-value'>{$paymentData['property_title']}</span>
                    </div>
                    
                    <div class='detail-row'>
                        <span class='detail-label'>Property ID:</span>
                        <span class='detail-value'>{$paymentData['property_id']}</span>
                    </div>
                </div>
                
                <div class='next-steps'>
                    <h3>What Happens Next?</h3>
                    <div class='step'>
                        <strong>1. Email Confirmation:</strong> You have received this confirmation email.
                    </div>
                    <div class='step'>
                        <strong>2. Seller Notification:</strong> The seller has been notified of your payment.
                    </div>
                    <div class='step'>
                        <strong>3. Property Reserved:</strong> The property is now reserved for you.
                    </div>
                    <div class='step'>
                        <strong>4. Contact Seller:</strong> The seller will contact you to complete the transaction.
                    </div>
                </div>
                
                <div class='security-notice'>
                    <strong>🔒 Security Notice:</strong> Your payment was processed securely. 
                    Keep your transaction ID safe for future reference.
                </div>
                
                <p style='margin-top: 30px;'>
                    If you have any questions about this transaction, please don't hesitate to contact our support team.
                </p>
                
                <p>
                    Best regards,<br>
                    <strong>TX Properties Team</strong>
                </p>
            </div>
            
            <div class='footer'>
                <p>&copy; " . date('Y') . " TX Properties. All rights reserved.</p>
                <p>This is an automated message, please do not reply to this email.</p>
                <p>For support, contact: <EMAIL></p>
            </div>
        </div>
    </body>
    </html>";
}
?> 