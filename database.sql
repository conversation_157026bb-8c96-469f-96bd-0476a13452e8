-- Database schema for TX PROPERTIES real estate platform

-- Create database
CREATE DATABASE IF NOT EXISTS txproperties;
USE txproperties;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('buyer', 'seller', 'admin') NOT NULL DEFAULT 'buyer',
    full_name VARCHAR(100),
    phone VARCHAR(20),
    balance DECIMAL(12,2) DEFAULT 0.00,
    profile_image VARCHAR(255),
    bio TEXT,
    verification_status BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Properties table
CREATE TABLE IF NOT EXISTS properties (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    price DECIMAL(12,2) NOT NULL,
    location VARCHAR(100) NOT NULL,
    region VARCHAR(50),
    city VARCHAR(50),
    lat DECIMAL(10,8),
    lng DECIMAL(11,8),
    property_type ENUM('Land', 'House', 'Apartment', 'Commercial', 'Office') NOT NULL,
    listing_type ENUM('Sale', 'Rent') NOT NULL,
    bedrooms INT,
    bathrooms INT,
    area DECIMAL(10,2),
    seller_id INT NOT NULL,
    status ENUM('Available', 'Sold', 'Reserved') DEFAULT 'Available',
    featured BOOLEAN DEFAULT FALSE,
    views INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (seller_id) REFERENCES users(id),
    has_electricity BOOLEAN DEFAULT 0,
    has_gym BOOLEAN DEFAULT 0,
    has_air_condition BOOLEAN DEFAULT 0,
    has_security BOOLEAN DEFAULT 0,
    has_pool BOOLEAN DEFAULT 0
);

-- Property images table
CREATE TABLE IF NOT EXISTS property_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    property_id INT NOT NULL,
    image_path VARCHAR(255) NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE
);

-- Messages table
CREATE TABLE IF NOT EXISTS messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sender_id INT NOT NULL,
    receiver_id INT NOT NULL,
    property_id INT,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    read_status BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_id) REFERENCES users(id),
    FOREIGN KEY (receiver_id) REFERENCES users(id),
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE SET NULL
);

-- Payments table
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    property_id INT NOT NULL,
    buyer_id INT NOT NULL,
    seller_id INT NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    transaction_id VARCHAR(100),
    payment_method VARCHAR(50),
    FOREIGN KEY (property_id) REFERENCES properties(id),
    FOREIGN KEY (buyer_id) REFERENCES users(id),
    FOREIGN KEY (seller_id) REFERENCES users(id)
);

-- Favorites table
CREATE TABLE IF NOT EXISTS favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    buyer_id INT NOT NULL,
    property_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (buyer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
    UNIQUE KEY (buyer_id, property_id)
);

-- Ratings table
CREATE TABLE IF NOT EXISTS ratings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    seller_id INT NOT NULL,
    buyer_id INT NOT NULL,
    property_id INT,
    stars TINYINT NOT NULL CHECK (stars BETWEEN 1 AND 5),
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (seller_id) REFERENCES users(id),
    FOREIGN KEY (buyer_id) REFERENCES users(id),
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE SET NULL
);

-- Contact messages table
CREATE TABLE IF NOT EXISTS contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    user_id INT NULL,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Sample data for testing
INSERT INTO users (username, email, password, role, full_name, phone, balance, profile_image, bio, verification_status) VALUES
('johndoe', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'buyer', 'John Doe', '+255123456789', 500000.00, 'assets/images/users/john.jpg', 'Enthusiastic property buyer looking for the best deals in Tanzania.', TRUE),
('janesmith', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'seller', 'Jane Smith', '+255987654321', 1250000.00, 'assets/images/users/jane.jpg', 'Professional real estate agent with 10 years of experience in Tanzanian property market.', TRUE),
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'Admin User', '+255111222333', 0.00, 'assets/images/users/admin.jpg', 'TX Properties platform administrator.', TRUE),
('sarahbuyer', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'buyer', 'Sarah Johnson', '+255765432109', 750000.00, 'assets/images/users/sarah.jpg', 'Looking for a beachfront property in Zanzibar.', TRUE),
('michaelseller', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'seller', 'Michael Brown', '+255712345678', 350000.00, 'assets/images/users/michael.jpg', 'Real estate developer specializing in modern apartments.', FALSE);

-- Sample properties
INSERT INTO properties (title, description, price, location, region, city, lat, lng, property_type, listing_type, bedrooms, bathrooms, area, seller_id, featured, views) VALUES
('Luxury Villa in Dar es Salaam', 'Beautiful luxury villa with ocean view in Masaki', 350000.00, 'Masaki, Dar es Salaam', 'Dar es Salaam', 'Dar es Salaam', -6.752, 39.2583, 'House', 'Sale', 4, 3, 250.00, 2, TRUE, 145),
('Modern Apartment in Arusha', 'Newly built modern apartment in central Arusha', 150000.00, 'Central Arusha', 'Arusha', 'Arusha', -3.3667, 36.6833, 'Apartment', 'Sale', 2, 2, 120.00, 2, TRUE, 78),
('Land Plot in Dodoma', 'Prime location land for development in the capital city', 75000.00, 'Njedengwa, Dodoma', 'Dodoma', 'Dodoma', -6.1630, 35.7516, 'Land', 'Sale', NULL, NULL, 500.00, 2, FALSE, 56),
('Rental House in Mwanza', 'Furnished family house for rent near Lake Victoria', 800.00, 'Ilemela, Mwanza', 'Mwanza', 'Mwanza', -2.5164, 32.9175, 'House', 'Rent', 3, 2, 180.00, 2, TRUE, 92),
('Commercial Space in Zanzibar', 'Commercial property in Stone Town perfect for retail', 1200.00, 'Stone Town, Zanzibar', 'Zanzibar', 'Zanzibar City', -6.1658, 39.1855, 'Commercial', 'Rent', NULL, NULL, 85.00, 2, FALSE, 63),
('Beach Front Land in Bagamoyo', 'Rare opportunity to own beachfront land in historical town', 120000.00, 'Kaole, Bagamoyo', 'Pwani', 'Bagamoyo', -6.4381, 38.9081, 'Land', 'Sale', NULL, NULL, 1000.00, 2, TRUE, 104);

-- Sample property images
INSERT INTO property_images (property_id, image_path, is_primary) VALUES
(1, 'assets/images/property1_main.jpg', TRUE),
(1, 'assets/images/property1_2.jpg', FALSE),
(1, 'assets/images/property1_3.jpg', FALSE),
(2, 'assets/images/property2_main.jpg', TRUE),
(2, 'assets/images/property2_2.jpg', FALSE),
(3, 'assets/images/property3_main.jpg', TRUE),
(4, 'assets/images/property4_main.jpg', TRUE),
(4, 'assets/images/property4_2.jpg', FALSE),
(5, 'assets/images/property5_main.jpg', TRUE),
(6, 'assets/images/property6_main.jpg', TRUE),
(6, 'assets/images/property6_2.jpg', FALSE);

-- Sample messages
INSERT INTO messages (sender_id, receiver_id, property_id, subject, message, read_status, created_at) VALUES
(1, 2, 1, 'Interest in Luxury Villa', 'Hello, I am interested in your Luxury Villa in Dar es Salaam. Can we arrange a viewing this weekend?', FALSE, NOW() - INTERVAL 2 DAY),
(4, 2, 3, 'Questions about the Land Plot', 'Hi, I have some questions regarding the land plot in Dodoma. Is it suitable for commercial development?', TRUE, NOW() - INTERVAL 5 DAY),
(1, 2, 2, 'Price negotiation for Apartment', 'Dear Jane, would you consider a slight discount on the apartment price? I am very interested in purchasing it.', FALSE, NOW() - INTERVAL 1 DAY);

-- Sample payments
INSERT INTO payments (property_id, buyer_id, seller_id, amount, date, status, transaction_id, payment_method) VALUES
(4, 1, 2, 800.00, NOW() - INTERVAL 10 DAY, 'completed', 'TXN123456789', 'Credit Card'),
(5, 4, 2, 1200.00, NOW() - INTERVAL 7 DAY, 'completed', 'TXN987654321', 'Mobile Money'),
(3, 1, 2, 75000.00, NOW() - INTERVAL 2 DAY, 'pending', 'TXN456789123', 'Bank Transfer');

-- Sample favorites
INSERT INTO favorites (buyer_id, property_id, created_at) VALUES
(1, 1, NOW() - INTERVAL 5 DAY),
(1, 6, NOW() - INTERVAL 3 DAY),
(4, 2, NOW() - INTERVAL 6 DAY),
(4, 5, NOW() - INTERVAL 2 DAY);

-- Sample ratings
INSERT INTO ratings (seller_id, buyer_id, property_id, stars, comment, created_at) VALUES
(2, 1, 4, 5, 'Excellent property and very responsive seller. The house was exactly as described.', NOW() - INTERVAL 8 DAY),
(2, 4, 5, 4, 'Good commercial space, well-located. The process was smooth.', NOW() - INTERVAL 5 DAY);

-- Add columns to properties table
ALTER TABLE properties
  ADD COLUMN has_electricity BOOLEAN DEFAULT 0,
  ADD COLUMN has_gym BOOLEAN DEFAULT 0,
  ADD COLUMN has_air_condition BOOLEAN DEFAULT 0,
  ADD COLUMN has_security BOOLEAN DEFAULT 0,
  ADD COLUMN has_pool BOOLEAN DEFAULT 0;