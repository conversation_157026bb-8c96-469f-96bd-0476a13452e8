<?php
// Include header and required files
require_once 'includes/header.php';
require_once 'includes/functions.php';

// Get property locations and types for the dropdowns
$locations = getPropertyLocations();
$propertyTypes = getPropertyTypes();

// Process search parameters
$searchCriteria = [];

if (isset($_GET['location']) && !empty($_GET['location'])) {
    $searchCriteria['location'] = sanitize($_GET['location']);
}

if (isset($_GET['type']) && !empty($_GET['type'])) {
    $searchCriteria['type'] = sanitize($_GET['type']);
}

if (isset($_GET['listing']) && !empty($_GET['listing'])) {
    $searchCriteria['listing'] = sanitize($_GET['listing']);
}

// Get search results
$properties = searchProperties($searchCriteria);

// Count results
$resultCount = count($properties);

// Define Tanzania regions
$regions = [
    'Arusha',
    'Dar es Salaam',
    'Dodoma',
    'Mbeya',
    'Tanga',
    'Zanzibar'
];

// Build page title based on search criteria
$pageTitle = "Properties";
if (!empty($searchCriteria['type'])) {
    $pageTitle = $searchCriteria['type'] . " " . $pageTitle;
}
if (!empty($searchCriteria['listing'])) {
    $pageTitle = $pageTitle . " for " . $searchCriteria['listing'];
}
if (!empty($searchCriteria['location'])) {
    $pageTitle = $pageTitle . " in " . $searchCriteria['location'];
}
?>

<!-- Page Header -->
<div class="container">
    <div class="row my-4">
        <div class="col-12">
            <h1><?php echo $pageTitle; ?></h1>
            <p class="text-muted"><?php echo $resultCount; ?> properties found</p>
        </div>
    </div>
</div>

<!-- Search Form -->
<div class="container mb-4">
    <div class="card">
        <div class="card-body">
            <form action="search.php" method="GET" onsubmit="filterProperties(event)">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="location" class="form-label">Location</label>
                        <select class="form-select" id="location" name="location">
                            <option value="">All Locations</option>
                            <?php foreach ($regions as $region): ?>
                                <option value="<?php echo $region; ?>" <?php echo (isset($searchCriteria['location']) && $searchCriteria['location'] == $region) ? 'selected' : ''; ?>>
                                    <?php echo $region; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="property-type" class="form-label">Property Type</label>
                        <select class="form-select" id="property-type" name="type">
                            <option value="">All Types</option>
                            <?php foreach ($propertyTypes as $type): ?>
                                <option value="<?php echo $type; ?>" <?php echo (isset($searchCriteria['type']) && $searchCriteria['type'] == $type) ? 'selected' : ''; ?>>
                                    <?php echo $type; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="listing-type" class="form-label">For</label>
                        <select class="form-select" id="listing-type" name="listing">
                            <option value="">Sale & Rent</option>
                            <option value="Sale" <?php echo (isset($searchCriteria['listing']) && $searchCriteria['listing'] == 'Sale') ? 'selected' : ''; ?>>Sale</option>
                            <option value="Rent" <?php echo (isset($searchCriteria['listing']) && $searchCriteria['listing'] == 'Rent') ? 'selected' : ''; ?>>Rent</option>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-search w-100">
                            <i class="fas fa-search me-1"></i> Search
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Search Results -->
<div class="container">
    <div class="row">
        <?php if (empty($properties)): ?>
            <div class="col-12">
                <div class="alert alert-info">
                    No properties found matching your criteria. Try adjusting your search parameters.
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($properties as $property): ?>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card property-card h-100">
                        <div class="position-relative">
                            <img src="<?php echo $property['primary_image']; ?>" class="card-img-top property-img" alt="<?php echo $property['title']; ?>">
                            <span class="badge position-absolute top-0 start-0 m-2 bg-<?php echo $property['status'] == 'Available' ? 'success' : ($property['status'] == 'Sold' ? 'danger' : 'secondary'); ?>"> <?php echo $property['status']; ?> </span>
                            <span class="badge position-absolute top-0 end-0 m-2 <?php echo $property['listing_type'] == 'Sale' ? 'badge-sale' : 'badge-rent'; ?>"> <?php echo $property['listing_type']; ?> </span>
                            <?php if ($property['featured']): ?>
                                <span class="badge bg-warning position-absolute top-0 start-50 translate-middle-x mt-2">Featured</span>
                            <?php endif; ?>
                            <a href="#" class="btn btn-sm btn-light position-absolute top-0 end-0 m-2" onclick="toggleFavorite(event, <?php echo $property['id']; ?>)">
                                <i class="far fa-heart"></i>
                            </a>
                        </div>
                        <div class="card-body">
                            <h5 class="property-title">
                                <a href="property.php?id=<?php echo $property['id']; ?>" class="text-decoration-none">
                                    <?php echo $property['title']; ?>
                                </a>
                            </h5>
                            <p class="property-price"><?php echo formatPrice($property['price'], $property['listing_type']); ?></p>
                            <p class="property-location"><i class="fas fa-map-marker-alt"></i> 
                                <?php 
                                if (isset($property['location_display'])) {
                                    echo $property['location_display'];
                                } else {
                                    echo $property['location'];
                                }
                                ?>
                            </p>
                            <p class="card-text"><?php echo substr($property['description'], 0, 100); ?>...</p>
                        </div>
                        <div class="card-footer bg-white">
                            <div class="property-features">
                                <?php if ($property['bedrooms']): ?>
                                    <span class="property-feature"><i class="fas fa-bed"></i> <?php echo $property['bedrooms']; ?> Beds</span>
                                <?php endif; ?>
                                <?php if ($property['bathrooms']): ?>
                                    <span class="property-feature"><i class="fas fa-bath"></i> <?php echo $property['bathrooms']; ?> Baths</span>
                                <?php endif; ?>
                                <?php if ($property['area']): ?>
                                    <span class="property-feature"><i class="fas fa-vector-square"></i> <?php echo $property['area']; ?> m²</span>
                                <?php endif; ?>
                                
                                <?php if (isset($property['has_electricity']) && $property['has_electricity']): ?>
                                    <span class="property-feature"><i class="fas fa-bolt text-warning"></i></span>
                                <?php endif; ?>
                                <?php if (isset($property['has_security']) && $property['has_security']): ?>
                                    <span class="property-feature"><i class="fas fa-shield-alt text-success"></i></span>
                                <?php endif; ?>
                                <?php if (isset($property['has_air_condition']) && $property['has_air_condition']): ?>
                                    <span class="property-feature"><i class="fas fa-snowflake text-info"></i></span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>

<?php
// Include footer
require_once 'includes/footer.php';
?> 