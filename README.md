# TX PROPERTIES - Real Estate Platform for Tanzania

TX PROPERTIES is a real estate online platform for Tanzania built using HTML, CSS, JavaScript, PHP, and MySQL. The platform allows users to browse, search, and list properties for sale or rent across Tanzania.

## Features

- Responsive design using Bootstrap 5
- User authentication (login, register, role-based access)
- Property search functionality by keyword, location, property type, and listing type
- Property listings with detailed information and image galleries
- Role-based navigation and access control
- Buyer and seller interfaces
- Contact forms for property inquiries

## Setup Instructions

### Prerequisites

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache, Nginx, etc.)
- XAMPP, WAMP, MAMP, or similar local development environment

### Installation Steps

1. **Clone or download the repository**
   - Download and extract the files to your web server directory (e.g., `htdocs` for XAMPP)

2. **Create the database**
   - Import the `database.sql` file into your MySQL database
   - You can use phpMyAdmin or MySQL command line:
     ```
     mysql -u username -p < database.sql
     ```

3. **Configure database connection**
   - Open `includes/config.php`
   - Update the database credentials if needed:
     ```php
     define('DB_HOST', 'localhost');
     define('DB_USER', 'root');  // Change if needed
     define('DB_PASS', '');      // Change if needed
     define('DB_NAME', 'txproperties');
     ```
   - Update the BASE_URL constant if needed:
     ```php
     define('BASE_URL', 'http://localhost/TXOnline/');
     ```

4. **Create folders for property images**
   - Make sure the `assets/images` directory is writable by the web server
   - You can use these commands:
     ```
     chmod -R 755 assets/images
     chown -R www-data:www-data assets/images
     ```

5. **Access the website**
   - Navigate to `http://localhost/TXOnline/` in your web browser

## Demo Accounts

For testing purposes, you can use the following accounts:

1. **Buyer Account**
   - Email: <EMAIL>
   - Password: password

2. **Seller Account**
   - Email: <EMAIL>
   - Password: password

3. **Admin Account**
   - Email: <EMAIL>
   - Password: password

## Project Structure

- `assets/` - Contains CSS, JavaScript, and image files
- `includes/` - Contains reusable PHP files (header, footer, configuration, functions)
- `index.php` - Homepage with search functionality and recent listings
- `search.php` - Property search results page
- `property.php` - Individual property details page
- `login.php` & `register.php` - User authentication pages
- `search_location.php` - Location-based search page
- Other files for various functionalities

## License

This project is licensed under the MIT License.

## Credits

- Bootstrap 5 - https://getbootstrap.com/
- Font Awesome - https://fontawesome.com/
- jQuery - https://jquery.com/ 