/* TX PROPERTIES ADMIN PANEL - Matching Main Site Theme */

/* Import the same fonts as main site */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

/* Admin Body Styling */
.admin-body {
    font-family: 'Inter', sans-serif;
    font-size: 0.95rem;
    background: var(--gradient-warm);
    min-height: 100vh;
    color: var(--text-color);
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
}

/* Admin Layout Wrapper for Fixed Footer */
.admin-layout-wrapper {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Admin Navigation Styling */
.admin-navbar {
    background: var(--gradient-primary) !important;
    box-shadow: 0 4px 20px rgba(44, 85, 48, 0.15);
    border: none;
    padding: 1rem 0;
    z-index: 1030;
}

/* Navbar Brand Styling */

.admin-brand {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    font-size: 1.5rem;
    color: white !important;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.admin-brand:hover {
    color: var(--beige-light) !important;
}

.brand-text {
    margin-right: 0.5rem;
}

.admin-badge {
    background: var(--secondary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 0.5rem;
}

/* Admin Navigation Links */
.admin-nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.75rem 1.25rem !important;
    border-radius: 8px;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
    position: relative;
}

.admin-nav-link:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.admin-nav-link.active {
    color: white !important;
    background: var(--secondary-color);
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(193, 119, 103, 0.3);
}

.admin-nav-link i {
    width: 20px;
    text-align: center;
}

/* Admin Dropdown */
.admin-dropdown {
    background: white;
    border: none;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
}

.admin-dropdown-item {
    color: var(--text-color);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.admin-dropdown-item:hover {
    background: var(--beige-light);
    color: var(--primary-color);
    transform: translateX(5px);
}

.admin-dropdown-item i {
    width: 20px;
    text-align: center;
}

/* Admin Content Wrapper */
.admin-content-wrapper {
    background: var(--gradient-warm);
    flex: 1;
    padding-bottom: 2rem;
    padding-top: 0;
}

/* Clean Text Layout */
.admin-content-wrapper h1,
.admin-content-wrapper h2,
.admin-content-wrapper h3,
.admin-content-wrapper h4,
.admin-content-wrapper h5,
.admin-content-wrapper h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    color: var(--primary-color);
    line-height: 1.3;
    margin-bottom: 1rem;
}

.admin-content-wrapper p {
    line-height: 1.6;
    margin-bottom: 1rem;
    color: var(--text-color);
}

.admin-content-wrapper .text-muted {
    color: var(--text-light) !important;
}

/* Admin Page Header */
.admin-page-header {
    background: white;
    border-radius: 0 0 15px 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    border-left: 4px solid var(--primary-color);
    margin-top: 0;
}

.admin-breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
}

.admin-breadcrumb .breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.admin-breadcrumb .breadcrumb-item a:hover {
    color: var(--secondary-color);
}

.admin-breadcrumb .breadcrumb-item.active {
    color: var(--text-light);
}

/* Admin Main Content */
.admin-main-content {
    background: white;
    border-radius: 0;
    padding: 2.5rem;
    box-shadow: none;
    min-height: calc(100vh - 140px);
    margin-bottom: 0;
    margin-top: 0;
}

/* Content Organization */
.admin-main-content .row {
    margin-bottom: 2rem;
}

.admin-main-content .row:last-child {
    margin-bottom: 0;
}

/* Clean spacing for content sections */
.admin-main-content section {
    margin-bottom: 3rem;
}

.admin-main-content section:last-child {
    margin-bottom: 0;
}

/* Dashboard Header Styling */
.admin-dashboard-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.admin-quick-actions .btn {
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.admin-quick-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

/* Section Headers */
.admin-stats-section h3,
.admin-properties-section h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    border-bottom: 2px solid var(--beige-medium);
    padding-bottom: 0.5rem;
}

/* Responsive Quick Actions */
@media (max-width: 768px) {
    .admin-dashboard-header .col-auto {
        margin-top: 1rem;
    }

    .admin-quick-actions {
        width: 100%;
    }

    .admin-quick-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .admin-quick-actions .btn:last-child {
        margin-bottom: 0;
    }
}

.nav-link:hover {
    color: #007bff;
}

.nav-link.active {
    color: #007bff;
}

.border-left-primary {
    border-left: 4px solid #4e73df !important;
}

.border-left-success {
    border-left: 4px solid #1cc88a !important;
}

.border-left-info {
    border-left: 4px solid #36b9cc !important;
}

.border-left-warning {
    border-left: 4px solid #f6c23e !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-primary {
    color: #4e73df !important;
}

.text-success {
    color: #1cc88a !important;
}

.text-info {
    color: #36b9cc !important;
}

.text-warning {
    color: #f6c23e !important;
}

/* Card styles */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.35rem;
}

.card-header {
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
    background-color: #f8f9fc;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* Utilities */
.font-weight-bold {
    font-weight: 700 !important;
}

.text-uppercase {
    text-transform: uppercase !important;
}

.text-xs {
    font-size: 0.7rem;
}

/* Media queries */
@media (max-width: 767.98px) {
    .sidebar {
        position: static;
        height: auto;
    }
    
    .sidebar-sticky {
        height: auto;
    }
}

/* Table styles */
.table-responsive {
    overflow-x: auto;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* ===== ADMIN THEME OVERRIDES ===== */

/* Override Bootstrap buttons with admin theme */
.btn-primary {
    background: var(--gradient-primary) !important;
    border: none !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    padding: 0.75rem 1.5rem !important;
    transition: all 0.3s ease !important;
}

.btn-primary:hover {
    background: var(--gradient-accent) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 15px rgba(44, 85, 48, 0.3) !important;
}

.btn-secondary {
    background: var(--secondary-color) !important;
    border: none !important;
    color: white !important;
}

.btn-success {
    background: var(--success-color) !important;
    border: none !important;
}

.btn-warning {
    background: var(--warning-color) !important;
    border: none !important;
}

.btn-danger {
    background: var(--error-color) !important;
    border: none !important;
}

/* Override Bootstrap cards */
.card {
    border: none !important;
    border-radius: 15px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin-bottom: 2rem !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
}

.card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
}

.card-header {
    background: var(--gradient-primary) !important;
    color: white !important;
    padding: 1.5rem !important;
    border: none !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
}

.card-body {
    padding: 2rem !important;
}

/* Override Bootstrap tables */
.table {
    background: white !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
}

.table thead th {
    background: var(--gradient-primary) !important;
    color: white !important;
    font-weight: 600 !important;
    border: none !important;
    padding: 1rem !important;
}

.table tbody td {
    padding: 1rem !important;
    border-bottom: 1px solid var(--border-color) !important;
    vertical-align: middle !important;
}

.table tbody tr:hover {
    background: var(--beige-light) !important;
}

/* Override Bootstrap forms */
.form-control {
    border: 2px solid var(--border-color) !important;
    border-radius: 8px !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.95rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.15) !important;
    outline: none !important;
}

.form-label {
    font-weight: 600 !important;
    color: var(--text-color) !important;
    margin-bottom: 0.5rem !important;
}

/* Admin Page Titles */
.admin-page-title {
    font-family: 'Playfair Display', serif !important;
    font-weight: 600 !important;
    color: var(--primary-color) !important;
    margin-bottom: 2rem !important;
}

/* Admin Stats Cards */
.admin-stats-card {
    background: var(--gradient-primary) !important;
    color: white !important;
    border-radius: 15px !important;
    padding: 2rem !important;
    text-align: center !important;
    transition: all 0.3s ease !important;
    border: none !important;
}

.admin-stats-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 8px 30px rgba(44, 85, 48, 0.3) !important;
}

.admin-stats-card .icon {
    font-size: 3rem !important;
    margin-bottom: 1rem !important;
    opacity: 0.9 !important;
}

.admin-stats-card .number {
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    margin-bottom: 0.5rem !important;
}

.admin-stats-card .label {
    font-size: 1rem !important;
    opacity: 0.9 !important;
    font-weight: 500 !important;
}

/* Admin Alerts */
.alert {
    border: none !important;
    border-radius: 12px !important;
    padding: 1.5rem !important;
    font-weight: 500 !important;
}

.alert-success {
    background: var(--success-light) !important;
    color: var(--success-color) !important;
}

.alert-warning {
    background: var(--warning-light) !important;
    color: var(--warning-color) !important;
}

.alert-danger {
    background: var(--error-light) !important;
    color: var(--error-color) !important;
}

.alert-info {
    background: var(--info-light) !important;
    color: var(--info-color) !important;
}

/* Admin Footer */
.admin-footer {
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    margin-top: auto;
}

.admin-footer-fixed {
    position: relative;
    bottom: 0;
    width: 100%;
    z-index: 1020;
}

.admin-footer-brand {
    font-family: 'Playfair Display', serif;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.admin-footer-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.admin-footer-links {
    margin-bottom: 1rem;
}

.admin-footer-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    margin-left: 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.admin-footer-link:hover {
    color: white;
    transform: translateY(-1px);
}

.admin-footer-link.text-danger {
    color: #ff6b6b !important;
}

.admin-footer-link.text-danger:hover {
    color: #ff5252 !important;
}

.admin-footer-copyright {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    /* Mobile Navigation */
    .admin-navbar .navbar-nav.mx-auto {
        margin: 1rem 0 !important;
        text-align: center;
    }

    .admin-nav-link {
        padding: 1rem !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .admin-nav-link:last-child {
        border-bottom: none;
    }

    /* Mobile Content */
    .admin-main-content {
        padding: 1.5rem;
        margin-bottom: 1rem;
        min-height: calc(100vh - 120px);
    }

    .admin-dashboard-header {
        padding: 1rem !important;
    }

    /* Mobile Footer */
    .admin-footer .col-md-6 {
        text-align: center !important;
        margin-bottom: 1rem;
    }

    .admin-footer-links {
        margin-top: 1rem;
    }

    .admin-footer-link {
        display: block;
        margin: 0.5rem 0;
    }

    /* Mobile Stats Cards */
    .admin-stats-card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .admin-content-wrapper {
        padding-bottom: 1rem;
    }

    .admin-main-content {
        padding: 1rem;
    }

    .admin-page-header {
        padding: 1rem;
    }

    .admin-stats-card {
        padding: 1.5rem;
    }

    .admin-stats-card .number {
        font-size: 2rem !important;
    }
}

/* ===== ENHANCED TYPOGRAPHY & SPACING ===== */

/* Clean Typography Hierarchy */
.admin-main-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.admin-main-content h2 {
    font-size: 2rem;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1.25rem;
}

.admin-main-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 1rem;
}

.admin-main-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 0.75rem;
}

.admin-main-content h5 {
    font-size: 1.1rem;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

/* Consistent Spacing */
.admin-main-content .mb-section {
    margin-bottom: 3rem;
}

.admin-main-content .mb-subsection {
    margin-bottom: 2rem;
}

.admin-main-content .mb-element {
    margin-bottom: 1rem;
}

/* Text Alignment and Organization */
.admin-text-center {
    text-align: center;
}

.admin-text-left {
    text-align: left;
}

.admin-text-right {
    text-align: right;
}

/* Enhanced List Styling */
.admin-main-content ul,
.admin-main-content ol {
    padding-left: 1.5rem;
    margin-bottom: 1rem;
}

.admin-main-content li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

/* Clean Dividers */
.admin-divider {
    border: none;
    height: 2px;
    background: var(--gradient-primary);
    margin: 2rem 0;
    border-radius: 1px;
}

.admin-divider-light {
    border: none;
    height: 1px;
    background: var(--border-color);
    margin: 1.5rem 0;
}

/* Content Blocks */
.admin-content-block {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-left: 4px solid var(--primary-color);
}

.admin-content-block h3 {
    margin-top: 0;
    color: var(--primary-color);
}

/* Info Boxes */
.admin-info-box {
    background: var(--beige-light);
    border: 1px solid var(--beige-medium);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.admin-info-box.success {
    background: rgba(5, 150, 105, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

.admin-info-box.warning {
    background: rgba(217, 119, 6, 0.1);
    border-color: var(--warning-color);
    color: var(--warning-color);
}

.admin-info-box.danger {
    background: rgba(220, 38, 38, 0.1);
    border-color: var(--error-color);
    color: var(--error-color);
}