<?php
ob_start();
session_start();
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}
require_once '../includes/config.php';
include 'admin_header.php';

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: properties.php');
    exit;
}
$id = (int)$_GET['id'];

// Fetch property
$sql = "SELECT * FROM properties WHERE id = $id";
$result = $conn->query($sql);
$property = $result->fetch_assoc();
if (!$property) {
    echo '<div class="alert alert-danger">Property not found.</div>';
    include 'admin_footer.php';
    ob_end_flush();
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title']);
    $type = trim($_POST['property_type']);
    $price = floatval($_POST['price']);
    $status = trim($_POST['status']);
    $location = trim($_POST['location']);
    $local_area = trim($_POST['local_area'] ?? '');

    // Property features
    $hasElectricity = isset($_POST['has_electricity']) ? 1 : 0;
    $hasWater = isset($_POST['has_water']) ? 1 : 0;
    $hasFence = isset($_POST['has_fence']) ? 1 : 0;
    $hasGym = isset($_POST['has_gym']) ? 1 : 0;
    $hasAirCondition = isset($_POST['has_air_condition']) ? 1 : 0;
    $hasSecurity = isset($_POST['has_security']) ? 1 : 0;
    $hasPool = isset($_POST['has_pool']) ? 1 : 0;

    // Check if feature columns exist
    $checkColumns = $conn->query("SHOW COLUMNS FROM properties LIKE 'local_area'");
    $hasFeatureColumns = $checkColumns->num_rows > 0;

    if ($hasFeatureColumns) {
        // Update with all feature columns
        $sql = "UPDATE properties SET title=?, property_type=?, price=?, status=?, location=?, local_area=?,
                has_electricity=?, has_water=?, has_fence=?, has_gym=?, has_air_condition=?, has_security=?, has_pool=?
                WHERE id=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('ssdsssiiiiiiii', $title, $type, $price, $status, $location, $local_area,
                         $hasElectricity, $hasWater, $hasFence, $hasGym, $hasAirCondition, $hasSecurity, $hasPool, $id);
    } else {
        // Update without feature columns
        $sql = "UPDATE properties SET title=?, property_type=?, price=?, status=?, location=? WHERE id=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('ssdssi', $title, $type, $price, $status, $location, $id);
    }

    if ($stmt->execute()) {
        header('Location: properties.php');
        ob_end_flush();
        exit;
    } else {
        echo '<div class="alert alert-danger">Failed to update property.</div>';
    }
}
?>
<div class="container-fluid">
    <h1 class="mb-4">Edit Property</h1>
    <form method="post" class="mb-4" style="max-width:600px;">
        <div class="mb-3">
            <label for="title" class="form-label">Title</label>
            <input type="text" class="form-control" id="title" name="title" value="<?php echo htmlspecialchars($property['title']); ?>" required>
        </div>
        <div class="mb-3">
            <label for="property_type" class="form-label">Type</label>
            <input type="text" class="form-control" id="property_type" name="property_type" value="<?php echo htmlspecialchars($property['property_type']); ?>" required>
        </div>
        <div class="mb-3">
            <label for="price" class="form-label">Price</label>
            <input type="number" class="form-control" id="price" name="price" value="<?php echo htmlspecialchars($property['price']); ?>" required>
        </div>
        <div class="mb-3">
            <label for="status" class="form-label">Status</label>
            <select class="form-select" id="status" name="status" required>
                <option value="Available" <?php if ($property['status'] == 'Available') echo 'selected'; ?>>Available</option>
                <option value="Sold" <?php if ($property['status'] == 'Sold') echo 'selected'; ?>>Sold</option>
                <option value="Rented" <?php if ($property['status'] == 'Rented') echo 'selected'; ?>>Rented</option>
            </select>
        </div>
        <div class="mb-3">
            <label for="location" class="form-label">Location</label>
            <input type="text" class="form-control" id="location" name="location" value="<?php echo htmlspecialchars($property['location']); ?>" required>
        </div>
        <?php
        // Check if local_area column exists before showing the field
        $checkColumns = $conn->query("SHOW COLUMNS FROM properties LIKE 'local_area'");
        $hasLocalArea = $checkColumns->num_rows > 0;
        if ($hasLocalArea):
        ?>
        <div class="mb-3">
            <label for="local_area" class="form-label">Local Area</label>
            <input type="text" class="form-control" id="local_area" name="local_area" value="<?php echo htmlspecialchars($property['local_area'] ?? ''); ?>">
        </div>
        <?php endif; ?>

        <?php
        // Check if feature columns exist before showing the fields
        $checkFeatureColumns = $conn->query("SHOW COLUMNS FROM properties LIKE 'has_electricity'");
        $hasFeatureColumns = $checkFeatureColumns->num_rows > 0;
        if ($hasFeatureColumns):
        ?>
        <div class="row mt-4">
            <div class="col-12 mb-2">
                <label class="form-label">Additional Features</label>
            </div>
            <div class="col-md-4 mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="has_electricity" name="has_electricity" value="1" <?php echo isset($property['has_electricity']) && $property['has_electricity'] ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="has_electricity">
                        <i class="fas fa-bolt text-warning me-2"></i> Electricity
                    </label>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="has_water" name="has_water" value="1" <?php echo isset($property['has_water']) && $property['has_water'] ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="has_water">
                        <i class="fas fa-tint text-info me-2"></i> Water
                    </label>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="has_fence" name="has_fence" value="1" <?php echo isset($property['has_fence']) && $property['has_fence'] ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="has_fence">
                        <i class="fas fa-border-all text-secondary me-2"></i> Fence
                    </label>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="has_gym" name="has_gym" value="1" <?php echo isset($property['has_gym']) && $property['has_gym'] ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="has_gym">
                        <i class="fas fa-dumbbell text-primary me-2"></i> Gym
                    </label>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="has_air_condition" name="has_air_condition" value="1" <?php echo isset($property['has_air_condition']) && $property['has_air_condition'] ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="has_air_condition">
                        <i class="fas fa-snowflake text-info me-2"></i> Air Conditioning
                    </label>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="has_security" name="has_security" value="1" <?php echo isset($property['has_security']) && $property['has_security'] ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="has_security">
                        <i class="fas fa-shield-alt text-success me-2"></i> Security
                    </label>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="has_pool" name="has_pool" value="1" <?php echo isset($property['has_pool']) && $property['has_pool'] ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="has_pool">
                        <i class="fas fa-swimming-pool text-primary me-2"></i> Swimming Pool
                    </label>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <button type="submit" class="btn btn-primary">Save Changes</button>
        <a href="properties.php" class="btn btn-secondary">Cancel</a>
    </form>
</div>
<?php include 'admin_footer.php'; ob_end_flush(); ?> 