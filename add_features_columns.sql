-- Add missing feature columns to properties table
-- Run this in phpMyAdmin or MySQL command line

USE txproperties;

-- Add all missing feature columns
ALTER TABLE properties ADD COLUMN IF NOT EXISTS has_electricity TINYINT(1) DEFAULT 0;
ALTER TABLE properties ADD COLUMN IF NOT EXISTS has_water TINYINT(1) DEFAULT 0;
ALTER TABLE properties ADD COLUMN IF NOT EXISTS has_fence TINYINT(1) DEFAULT 0;
ALTER TABLE properties ADD COLUMN IF NOT EXISTS has_gym TINYINT(1) DEFAULT 0;
ALTER TABLE properties ADD COLUMN IF NOT EXISTS has_air_condition TINYINT(1) DEFAULT 0;
ALTER TABLE properties ADD COLUMN IF NOT EXISTS has_security TINYINT(1) DEFAULT 0;
ALTER TABLE properties ADD COLUMN IF NOT EXISTS has_pool TINYINT(1) DEFAULT 0;

-- Verify the columns were added
DESCRIBE properties; 