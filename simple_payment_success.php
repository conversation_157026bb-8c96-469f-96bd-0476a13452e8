<?php
// Simple Payment Success - Bypasses API verification for development
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Get parameters from URL
$transactionId = isset($_GET['transaction_id']) ? $_GET['transaction_id'] : 'DEV_TXN_' . time();
$propertyId = isset($_GET['property_id']) ? (int)$_GET['property_id'] : 0;
$amount = isset($_GET['amount']) ? floatval($_GET['amount']) : 0;
$sellerId = isset($_GET['seller_id']) ? (int)$_GET['seller_id'] : 0;
$sellerName = isset($_GET['seller_name']) ? $_GET['seller_name'] : '';

// Validate required parameters
if (!$propertyId || !$amount || !$sellerId) {
    die('Missing required payment parameters');
}

// Get property details
$property = getPropertyById($propertyId);
if (!$property) {
    die('Property not found');
}

$propertyTitle = $property['title'];
$buyerId = $_SESSION['user_id'] ?? 1; // Default to user 1 for testing
$buyerName = $_SESSION['username'] ?? 'Test Buyer';

try {
    // Start database transaction
    $conn->begin_transaction();
    
    // Insert payment record
    $sql = "INSERT INTO payments (property_id, buyer_id, seller_id, amount, date, status, transaction_id, payment_method) VALUES (?, ?, ?, ?, NOW(), 'completed', ?, 'Flutterwave')";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('iiids', $propertyId, $buyerId, $sellerId, $amount, $transactionId);
    $stmt->execute();
    
    // Mark property as sold
    $sql = "UPDATE properties SET status = 'Sold' WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $propertyId);
    $stmt->execute();
    
    // Send messages to buyer and seller
    // To Buyer - Purchase Confirmation
    $buyerMsg = "Congratulations! You have successfully purchased the property titled \"$propertyTitle\". 

Payment Details:
- Property: $propertyTitle (ID: $propertyId)
- Amount Paid: TZS " . number_format($amount, 2) . "
- Transaction ID: $transactionId
- Payment Method: Flutterwave
- Date: " . date('F j, Y \a\t g:i A') . "

Thank you for your payment. The property is now yours! You can contact the seller for next steps regarding property transfer.

Best regards,
TX Properties Team";

    $buyerSubject = "Property Purchase Confirmation - $propertyTitle";
    $sql = "INSERT INTO messages (sender_id, receiver_id, property_id, subject, message, created_at) VALUES (?, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('iiiss', $sellerId, $buyerId, $propertyId, $buyerSubject, $buyerMsg);
    $stmt->execute();

    // To Seller - Sale Notification
    $sellerMsg = "Great news! Your property titled \"$propertyTitle\" has been sold via Flutterwave.

Sale Details:
- Property: $propertyTitle (ID: $propertyId)
- Buyer: $buyerName
- Sale Amount: TZS " . number_format($amount, 2) . "
- Transaction ID: $transactionId
- Payment Method: Flutterwave
- Date: " . date('F j, Y \a\t g:i A') . "

The payment has been processed successfully and your property is now marked as SOLD. Please coordinate with the buyer for property transfer procedures.

Congratulations on your successful sale!

Best regards,
TX Properties Team";

    $sellerSubject = "Property Sold - $propertyTitle";
    $sql = "INSERT INTO messages (sender_id, receiver_id, property_id, subject, message, created_at) VALUES (?, ?, ?, ?, ?, NOW())";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('iiiss', $buyerId, $sellerId, $propertyId, $sellerSubject, $sellerMsg);
    $stmt->execute();
    
    // Commit transaction
    $conn->commit();
    
    // Success! Show success page
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Payment Successful</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card shadow-lg">
                        <div class="card-body text-center p-5">
                            <div class="mb-4">
                                <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                            </div>
                            <h2 class="text-success mb-3">Payment Successful!</h2>
                            <h4 class="mb-4">Congratulations on your purchase!</h4>
                            
                            <div class="alert alert-success">
                                <h5 class="mb-3">Purchase Details:</h5>
                                <div class="row text-start">
                                    <div class="col-sm-4"><strong>Property:</strong></div>
                                    <div class="col-sm-8"><?php echo htmlspecialchars($propertyTitle); ?></div>
                                </div>
                                <div class="row text-start">
                                    <div class="col-sm-4"><strong>Amount Paid:</strong></div>
                                    <div class="col-sm-8">TZS <?php echo number_format($amount, 2); ?></div>
                                </div>
                                <div class="row text-start">
                                    <div class="col-sm-4"><strong>Transaction ID:</strong></div>
                                    <div class="col-sm-8"><?php echo $transactionId; ?></div>
                                </div>
                                <div class="row text-start">
                                    <div class="col-sm-4"><strong>Date:</strong></div>
                                    <div class="col-sm-8"><?php echo date('F j, Y \a\t g:i A'); ?></div>
                                </div>
                            </div>
                            
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>What happens next?</h6>
                                <ul class="text-start mb-0">
                                    <li>The property is now marked as <strong>SOLD</strong></li>
                                    <li>You and the seller have received confirmation messages</li>
                                    <li>The seller will contact you for property transfer procedures</li>
                                    <li>Check your inbox for detailed purchase confirmation</li>
                                    <li>The property has been removed from public listings</li>
                                </ul>
                            </div>
                            
                            <div class="d-flex gap-3 justify-content-center flex-wrap mt-4">
                                <a href="property.php?id=<?php echo $propertyId; ?>" class="btn btn-primary">
                                    <i class="fas fa-eye me-2"></i>View Property
                                </a>
                                <a href="my_paid_properties.php" class="btn btn-success">
                                    <i class="fas fa-home me-2"></i>My Properties
                                </a>
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="fas fa-search me-2"></i>Browse More
                                </a>
                            </div>
                            
                            <p class="text-muted mt-4">This window will close automatically in <span id="countdown">10</span> seconds.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            let countdown = 10;
            const countdownElement = document.getElementById('countdown');
            
            const timer = setInterval(function() {
                countdown--;
                countdownElement.textContent = countdown;
                
                if (countdown <= 0) {
                    clearInterval(timer);
                    if (window.opener) {
                        window.opener.location.href = 'index.php?payment_success=1';
                    }
                    window.close();
                }
            }, 1000);
        </script>
    </body>
    </html>
    <?php
    
} catch (Exception $e) {
    // Rollback on error
    $conn->rollback();
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Payment Error</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-times-circle text-danger" style="font-size: 3rem;"></i>
                            <h3 class="text-danger mt-3">Payment Processing Error</h3>
                            <p class="text-muted"><?php echo htmlspecialchars($e->getMessage()); ?></p>
                            <a href="property.php?id=<?php echo $propertyId; ?>" class="btn btn-primary">Back to Property</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
}

$conn->close();
?>
