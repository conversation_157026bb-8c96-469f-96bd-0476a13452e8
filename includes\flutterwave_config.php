<?php
// Flutterwave API Configuration
// Store your Flutterwave API keys here

// Test Environment Keys
define('FLUTTERWAVE_PUBLIC_KEY', 'FLWPUBK_TEST-d4f1bb786cb335b4112efdcd3f38cfb6-X');
define('FLUTTERWAVE_SECRET_KEY', 'FLWSECK_TEST-05affa6a2278a5f791eba147273fcc1a-X');
define('FLUTTERWAVE_ENCRYPTION_KEY', 'FLWSECK_TESTf46c075d9948');

// API Endpoints
define('FLUTTERWAVE_BASE_URL', 'https://api.flutterwave.com/v3');
define('FLUTTERWAVE_VERIFY_URL', FLUTTERWAVE_BASE_URL . '/transactions/{id}/verify');

// Environment Settings
define('FLUTTERWAVE_ENVIRONMENT', 'test'); // 'test' or 'live'

/**
 * Get Flutterwave API headers for requests
 * @return array
 */
function getFlutterwaveHeaders() {
    return [
        'Authorization: Bearer ' . FLUTTERWAVE_SECRET_KEY,
        'Content-Type: application/json'
    ];
}

/**
 * Get Flutterwave public key for frontend
 * @return string
 */
function getFlutterwavePublicKey() {
    return FLUTTERWAVE_PUBLIC_KEY;
}

/**
 * Verify Flutterwave transaction
 * @param string $transactionId
 * @return array|false
 */
function verifyFlutterwaveTransaction($transactionId) {
    $url = str_replace('{id}', $transactionId, FLUTTERWAVE_VERIFY_URL);
    
    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTPHEADER => getFlutterwaveHeaders(),
    ));
    
    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    $err = curl_error($curl);
    curl_close($curl);
    
    // Log the response for debugging
    error_log("Flutterwave Verification - Transaction ID: $transactionId");
    error_log("Flutterwave Verification - HTTP Code: $httpCode");
    error_log("Flutterwave Verification - Response: $response");
    
    if ($err) {
        error_log("Flutterwave Verification - cURL Error: $err");
        return false;
    }
    
    if ($httpCode !== 200) {
        error_log("Flutterwave Verification - HTTP Error: $httpCode");
        return false;
    }
    
    $result = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("Flutterwave Verification - JSON Error: " . json_last_error_msg());
        return false;
    }
    
    return $result;
}

/**
 * Check if Flutterwave transaction is successful
 * @param array $verificationResult
 * @return bool
 */
function isFlutterwaveTransactionSuccessful($verificationResult) {
    if (!$verificationResult || !isset($verificationResult['data'])) {
        return false;
    }
    
    $data = $verificationResult['data'];
    
    // Check main status
    if (isset($verificationResult['status']) && $verificationResult['status'] === 'success') {
        // Check transaction status
        if (isset($data['status'])) {
            $transactionStatus = strtolower($data['status']);
            $successStatuses = ['successful', 'completed', 'success'];
            
            if (in_array($transactionStatus, $successStatuses)) {
                return true;
            }
        }
    }
    
    // Alternative check for direct status
    if (isset($data['status'])) {
        $transactionStatus = strtolower($data['status']);
        $successStatuses = ['successful', 'completed', 'success'];
        
        if (in_array($transactionStatus, $successStatuses)) {
            return true;
        }
    }
    
    return false;
}

/**
 * Get transaction details from verification result
 * @param array $verificationResult
 * @return array
 */
function getTransactionDetails($verificationResult) {
    $details = [
        'transaction_id' => '',
        'amount' => 0,
        'currency' => '',
        'status' => '',
        'customer_email' => '',
        'customer_name' => ''
    ];
    
    if (isset($verificationResult['data'])) {
        $data = $verificationResult['data'];
        
        $details['transaction_id'] = $data['id'] ?? '';
        $details['amount'] = $data['amount'] ?? 0;
        $details['currency'] = $data['currency'] ?? '';
        $details['status'] = $data['status'] ?? '';
        
        if (isset($data['customer'])) {
            $details['customer_email'] = $data['customer']['email'] ?? '';
            $details['customer_name'] = $data['customer']['name'] ?? '';
        }
    }
    
    return $details;
}
?>
