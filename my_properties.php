<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check authentication BEFORE including header
if (!isLoggedIn() || $_SESSION['user_role'] !== 'seller') {
    redirect('login.php');
}

// All redirects done, now safe to include header
require_once 'includes/header.php';

// Fetch all properties for this seller
$sql = "SELECT p.*, (SELECT image_path FROM property_images WHERE property_id = p.id AND is_primary = 1 LIMIT 1) as primary_image FROM properties p WHERE seller_id = ? ORDER BY created_at DESC";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
$properties = [];
while ($row = $result->fetch_assoc()) {
    if (empty($row['primary_image'])) {
        $row['primary_image'] = 'assets/images/property-default.jpg';
    }
    $properties[] = $row;
}

?>
<div class="container my-4">
    <h1 class="mb-4">My Properties</h1>
    <div class="row">
        <?php if (empty($properties)): ?>
            <div class="col-12">
                <div class="alert alert-info">You have not added any properties yet.</div>
            </div>
        <?php else: ?>
            <?php foreach ($properties as $property): ?>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card property-card h-100">
                        <div class="position-relative">
                            <img src="<?php echo $property['primary_image']; ?>" class="card-img-top property-img" alt="<?php echo $property['title']; ?>">
                            <span class="badge position-absolute top-0 start-0 m-2 bg-<?php echo $property['status'] == 'Available' ? 'success' : ($property['status'] == 'Sold' ? 'danger' : 'secondary'); ?>"> <?php echo $property['status']; ?> </span>
                            <span class="badge position-absolute top-0 end-0 m-2 <?php echo $property['listing_type'] == 'Sale' ? 'badge-sale' : 'badge-rent'; ?>"> <?php echo $property['listing_type']; ?> </span>
                        </div>
                        <div class="card-body">
                            <h5 class="property-title">
                                <?php echo htmlspecialchars($property['title']); ?>
                            </h5>
                            <p class="property-price"><?php echo number_format($property['price']); ?> TZS</p>
                            <p class="property-location"><i class="fas fa-map-marker-alt"></i> <?php echo !empty($property['local_area'] ?? '') ? $property['local_area'] . ', ' . $property['location'] : $property['location']; ?></p>
                        </div>
                        <div class="card-footer bg-white d-flex justify-content-between">
                            <a href="seller_edit_property.php?id=<?php echo $property['id']; ?>" class="btn btn-sm btn-primary">Edit</a>
                            <a href="seller_dashboard.php?delete=<?php echo $property['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Delete this property?');">Delete</a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>
<?php require_once 'includes/footer.php'; ?> 