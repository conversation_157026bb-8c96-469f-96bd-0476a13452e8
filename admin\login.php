<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/functions.php';

$error = '';

if (isset($_POST['username'], $_POST['password'])) {
    $username = sanitize($_POST['username']);
    $password = $_POST['password'];

    $sql = "SELECT * FROM users WHERE username = ? AND role = 'admin' LIMIT 1";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('s', $username);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();

    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_id'] = $user['id'];
        $_SESSION['admin_username'] = $user['username'];
        header('Location: index.php');
        exit;
    } else {
        $error = 'Invalid admin credentials.';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - TX Properties</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Same as main site -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Main Site CSS for theme consistency -->
    <link href="../assets/css/style.css" rel="stylesheet">

    <!-- Admin-specific CSS -->
    <link href="../assets/css/admin.css" rel="stylesheet">
</head>
<body style="background: var(--gradient-warm); min-height: 100vh; font-family: 'Inter', sans-serif;">
    <div class="container d-flex align-items-center justify-content-center" style="min-height: 100vh;">
        <div class="row justify-content-center w-100">
            <div class="col-md-6 col-lg-5">
                <div class="card admin-card shadow-lg border-0">
                    <div class="card-body p-5">
                        <!-- Admin Login Header -->
                        <div class="text-center mb-4">
                            <div class="admin-login-icon mb-3">
                                <i class="fas fa-shield-alt fa-3x" style="color: var(--primary-color);"></i>
                            </div>
                            <h2 class="admin-page-title mb-2">Admin Portal</h2>
                            <p class="text-muted">TX Properties Administration</p>
                        </div>

                        <!-- Error Alert -->
                        <?php if ($error): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($error); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <!-- Login Form -->
                        <form method="post" action="" class="admin-login-form">
                            <div class="mb-4">
                                <label for="username" class="admin-form-label">
                                    <i class="fas fa-user me-2"></i>Administrator Username
                                </label>
                                <input type="text"
                                       class="form-control admin-form-control"
                                       id="username"
                                       name="username"
                                       placeholder="Enter your admin username"
                                       required
                                       autofocus>
                            </div>

                            <div class="mb-4">
                                <label for="password" class="admin-form-label">
                                    <i class="fas fa-lock me-2"></i>Password
                                </label>
                                <div class="position-relative">
                                    <input type="password"
                                           class="form-control admin-form-control pe-5"
                                           id="password"
                                           name="password"
                                           placeholder="Enter your password"
                                           required>
                                    <button type="button"
                                            class="btn btn-link position-absolute end-0 top-50 translate-middle-y pe-3"
                                            onclick="togglePassword('password')"
                                            style="border: none; background: none; color: var(--text-light);">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="d-grid gap-2 mb-4">
                                <button type="submit" class="btn admin-btn admin-btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>Access Admin Panel
                                </button>
                            </div>
                        </form>

                        <!-- Footer Links -->
                        <div class="text-center">
                            <hr class="my-4">
                            <p class="text-muted small mb-3">
                                <i class="fas fa-shield-alt me-1"></i>
                                Secure administrator access only
                            </p>
                            <a href="../index.php" class="text-decoration-none" style="color: var(--primary-color);">
                                <i class="fas fa-arrow-left me-1"></i>Back to Website
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
    // Password toggle function
    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const button = field.parentElement.querySelector('button');
        const icon = button.querySelector('i');
        
        if (field.type === 'password') {
            field.type = 'text';
            icon.className = 'fas fa-eye-slash';
            button.setAttribute('title', 'Hide password');
        } else {
            field.type = 'password';
            icon.className = 'fas fa-eye';
            button.setAttribute('title', 'Show password');
        }
        
        // Add animation
        button.style.transform = 'translateY(-50%) scale(1.1)';
        setTimeout(() => {
            button.style.transform = 'translateY(-50%) scale(1)';
        }, 150);
    }

    // Add smooth animations and enhanced UX
    document.addEventListener('DOMContentLoaded', function() {
        // Fade in the login card
        const card = document.querySelector('.admin-card');
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100);

        // Add loading state to login button
        const form = document.querySelector('.admin-login-form');
        form.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Authenticating...';
            submitBtn.disabled = true;
        });
    });
    </script>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>