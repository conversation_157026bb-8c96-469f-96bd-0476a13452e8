<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if user is logged in and is a buyer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'buyer') {
    redirect('login.php');
}

// Get payment details from URL parameters
$propertyId = isset($_GET['property_id']) ? (int)$_GET['property_id'] : 0;
$transactionId = isset($_GET['transaction_id']) ? $_GET['transaction_id'] : '';
$amount = isset($_GET['amount']) ? (float)$_GET['amount'] : 0;
$propertyTitle = isset($_GET['property_title']) ? urldecode($_GET['property_title']) : '';

// Verify this is a valid payment for the current user
if ($propertyId && $transactionId) {
    $sql = "SELECT p.*, prop.title as property_title 
            FROM payments p 
            JOIN properties prop ON p.property_id = prop.id 
            WHERE p.property_id = ? AND p.transaction_id = ? AND p.buyer_id = ? AND p.status = 'completed'";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('isi', $propertyId, $transactionId, $_SESSION['user_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $payment = $result->fetch_assoc();
        $propertyTitle = $payment['property_title'];
        $amount = $payment['amount'];
    } else {
        // Invalid payment details, redirect to home
        redirect('index.php');
    }
} else {
    redirect('index.php');
}

require_once 'includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <!-- Success Header -->
            <div class="text-center mb-5">
                <div class="mb-4">
                    <i class="fas fa-check-circle text-success" style="font-size: 5rem;"></i>
                </div>
                <h1 class="text-success mb-3">Payment Successful!</h1>
                <h3 class="text-muted">Congratulations on your property purchase!</h3>
            </div>
            
            <!-- Payment Details Card -->
            <div class="card shadow-lg mb-4">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0"><i class="fas fa-receipt me-2"></i>Purchase Receipt</h4>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="mb-3">Property Details:</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Property:</strong></td>
                                    <td><?php echo htmlspecialchars($propertyTitle); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Property ID:</strong></td>
                                    <td>#<?php echo $propertyId; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td><span class="badge bg-danger">SOLD</span></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5 class="mb-3">Payment Details:</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Amount Paid:</strong></td>
                                    <td class="text-success"><strong>TZS <?php echo number_format($amount, 2); ?></strong></td>
                                </tr>
                                <tr>
                                    <td><strong>Transaction ID:</strong></td>
                                    <td><code><?php echo htmlspecialchars($transactionId); ?></code></td>
                                </tr>
                                <tr>
                                    <td><strong>Payment Method:</strong></td>
                                    <td><i class="fas fa-credit-card me-2"></i>Flutterwave</td>
                                </tr>
                                <tr>
                                    <td><strong>Date & Time:</strong></td>
                                    <td><?php echo date('F j, Y \a\t g:i A', strtotime($payment['date'])); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Next Steps Card -->
            <div class="card shadow-lg mb-4">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0"><i class="fas fa-list-check me-2"></i>What Happens Next?</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-success"><i class="fas fa-check-circle me-2"></i>Completed Actions:</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>Payment processed successfully</li>
                                <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>Property marked as SOLD</li>
                                <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>Seller notified of sale</li>
                                <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>Purchase confirmation sent to your inbox</li>
                                <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>Property removed from public listings</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-primary"><i class="fas fa-clock me-2"></i>Next Steps:</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item"><i class="fas fa-envelope text-primary me-2"></i>Check your inbox for detailed confirmation</li>
                                <li class="list-group-item"><i class="fas fa-handshake text-primary me-2"></i>Seller will contact you for property transfer</li>
                                <li class="list-group-item"><i class="fas fa-file-contract text-primary me-2"></i>Prepare necessary documents for ownership transfer</li>
                                <li class="list-group-item"><i class="fas fa-key text-primary me-2"></i>Coordinate property handover with seller</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="text-center">
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a href="property.php?id=<?php echo $propertyId; ?>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-eye me-2"></i>View Property Details
                    </a>
                    <a href="my_paid_properties.php" class="btn btn-success btn-lg">
                        <i class="fas fa-home me-2"></i>My Purchased Properties
                    </a>
                    <a href="inbox.php" class="btn btn-info btn-lg">
                        <i class="fas fa-envelope me-2"></i>Check Messages
                    </a>
                    <a href="index.php" class="btn btn-secondary btn-lg">
                        <i class="fas fa-search me-2"></i>Browse More Properties
                    </a>
                </div>
            </div>
            
            <!-- Contact Support -->
            <div class="text-center mt-5">
                <div class="alert alert-light">
                    <h6><i class="fas fa-question-circle me-2"></i>Need Help?</h6>
                    <p class="mb-0">If you have any questions about your purchase, please <a href="contact.php">contact our support team</a> or check your <a href="inbox.php">inbox</a> for messages from the seller.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
