<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if user is logged in and is a seller
if (!isLoggedIn() || $_SESSION['user_role'] !== 'seller') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['image_id'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
    exit;
}

$imageId = (int)$_POST['image_id'];

// Fetch image and verify ownership
$sql = "SELECT pi.*, p.seller_id FROM property_images pi 
        JOIN properties p ON pi.property_id = p.id 
        WHERE pi.id = ? AND p.seller_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('ii', $imageId, $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
$image = $result->fetch_assoc();

if (!$image) {
    echo json_encode(['success' => false, 'message' => 'Image not found or you do not have permission to delete it']);
    exit;
}

// Delete the physical file
if (file_exists($image['image_path'])) {
    unlink($image['image_path']);
}

// Delete from database
$deleteSql = "DELETE FROM property_images WHERE id = ?";
$deleteStmt = $conn->prepare($deleteSql);
$deleteStmt->bind_param('i', $imageId);

if ($deleteStmt->execute()) {
    echo json_encode(['success' => true, 'message' => 'Image deleted successfully']);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to delete image from database']);
}
?> 